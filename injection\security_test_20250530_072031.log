2025-05-30 07:20:31,243 - INFO - Security tester initialized for target: https://bytesurfer.ghxst.me/projects.php
2025-05-30 07:20:31,244 - INFO - Starting comprehensive security test...
2025-05-30 07:20:31,251 - INFO - Testing basic connectivity...
2025-05-30 07:20:33,270 - INFO - SUCCESS: Successfully connected to https://bytesurfer.ghxst.me/projects.php
2025-05-30 07:20:33,272 - INFO - Response status: 200
2025-05-30 07:20:33,275 - INFO - Response headers: {'Connection': 'Keep-Alive', 'Keep-Alive': 'timeout=5, max=100', 'x-xss-protection': '1; mode=block', 'x-content-type-options': 'nosniff', 'x-frame-options': 'DENY', 'referrer-policy': 'strict-origin-when-cross-origin', 'x-permitted-cross-domain-policies': 'none', 'strict-transport-security': 'max-age=31536000; includeSubDomains; preload', 'permissions-policy': 'geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), speaker=(), vibrate=(), fullscreen=(), sync-xhr=()', 'cross-origin-embedder-policy': 'require-corp', 'cross-origin-opener-policy': 'same-origin', 'cross-origin-resource-policy': 'same-origin', 'content-security-policy': "default-src 'self'; script-src 'self' 'unsafe-inline' https://fonts.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self'; frame-ancestors 'none'; base-uri 'self'; form-action 'self'; upgrade-insecure-requests; block-all-mixed-content", 'set-cookie': 'PHPSESSID=urbolp3dukf640lsf2ogm1te9i; path=/; secure; HttpOnly; SameSite=Strict', 'expires': 'Thu, 01 Jan 1970 00:00:00 GMT', 'cache-control': 'no-cache, no-store, must-revalidate, private, max-age=0', 'pragma': 'no-cache', 'content-type': 'text/html; charset=UTF-8', 'transfer-encoding': 'chunked', 'content-encoding': 'gzip', 'vary': 'Accept-Encoding', 'date': 'Thu, 29 May 2025 23:20:32 GMT', 'platform': 'hostinger', 'panel': 'hpanel', 'alt-svc': 'h3=":443"; ma=2592000, h3-29=":443"; ma=2592000, h3-Q050=":443"; ma=2592000, h3-Q046=":443"; ma=2592000, h3-Q043=":443"; ma=2592000, quic=":443"; ma=2592000; v="43,46"'}
2025-05-30 07:20:33,286 - INFO - Checking security headers...
2025-05-30 07:20:33,287 - INFO - SUCCESS: Security header found: X-Content-Type-Options: nosniff
2025-05-30 07:20:33,288 - INFO - SUCCESS: Security header found: X-Frame-Options: DENY
2025-05-30 07:20:33,289 - INFO - SUCCESS: Security header found: X-XSS-Protection: 1; mode=block
2025-05-30 07:20:33,290 - INFO - SUCCESS: Security header found: Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
2025-05-30 07:20:33,291 - INFO - SUCCESS: Security header found: Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://fonts.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self'; frame-ancestors 'none'; base-uri 'self'; form-action 'self'; upgrade-insecure-requests; block-all-mixed-content
2025-05-30 07:20:33,294 - INFO - SUCCESS: Security header found: Referrer-Policy: strict-origin-when-cross-origin
2025-05-30 07:20:33,299 - INFO - SUCCESS: Security header found: X-Permitted-Cross-Domain-Policies: none
2025-05-30 07:20:33,301 - INFO - Creating test project entry 'joe'...
2025-05-30 07:20:33,304 - INFO - SUCCESS: Test project 'joe' created successfully
2025-05-30 07:20:33,305 - INFO - Testing GET parameter injection...
2025-05-30 07:20:33,306 - INFO - Testing GET parameter: id
2025-05-30 07:20:33,308 - INFO - Testing php_injection payloads on parameter id
2025-05-30 07:20:35,412 - INFO - Testing xss payloads on parameter id
2025-05-30 07:20:37,545 - INFO - Testing sql_injection payloads on parameter id
2025-05-30 07:20:39,671 - INFO - Testing command_injection payloads on parameter id
2025-05-30 07:20:41,998 - INFO - Testing path_traversal payloads on parameter id
2025-05-30 07:20:44,156 - INFO - Testing json_injection payloads on parameter id
2025-05-30 07:20:46,368 - INFO - Testing header_injection payloads on parameter id
2025-05-30 07:20:48,470 - INFO - Testing file_upload payloads on parameter id
2025-05-30 07:20:50,578 - INFO - Testing ldap_injection payloads on parameter id
2025-05-30 07:20:52,668 - INFO - Testing template_injection payloads on parameter id
2025-05-30 07:20:54,760 - INFO - Testing GET parameter: name
2025-05-30 07:20:54,761 - INFO - Testing php_injection payloads on parameter name
2025-05-30 07:20:56,851 - INFO - Testing xss payloads on parameter name
2025-05-30 07:20:58,973 - INFO - Testing sql_injection payloads on parameter name
2025-05-30 07:21:01,099 - INFO - Testing command_injection payloads on parameter name
2025-05-30 07:21:03,201 - INFO - Testing path_traversal payloads on parameter name
2025-05-30 07:21:05,298 - INFO - Testing json_injection payloads on parameter name
2025-05-30 07:21:07,393 - INFO - Testing header_injection payloads on parameter name
2025-05-30 07:21:09,485 - INFO - Testing file_upload payloads on parameter name
2025-05-30 07:21:11,612 - INFO - Testing ldap_injection payloads on parameter name
2025-05-30 07:21:13,704 - INFO - Testing template_injection payloads on parameter name
2025-05-30 07:21:15,790 - INFO - Testing GET parameter: search
2025-05-30 07:21:15,791 - INFO - Testing php_injection payloads on parameter search
2025-05-30 07:21:17,876 - INFO - Testing xss payloads on parameter search
2025-05-30 07:21:19,975 - INFO - Testing sql_injection payloads on parameter search
2025-05-30 07:21:22,059 - INFO - Testing command_injection payloads on parameter search
2025-05-30 07:21:24,143 - INFO - Testing path_traversal payloads on parameter search
2025-05-30 07:21:26,231 - INFO - Testing json_injection payloads on parameter search
2025-05-30 07:21:28,315 - INFO - Testing header_injection payloads on parameter search
2025-05-30 07:21:30,398 - INFO - Testing file_upload payloads on parameter search
2025-05-30 07:21:32,480 - INFO - Testing ldap_injection payloads on parameter search
2025-05-30 07:21:34,565 - INFO - Testing template_injection payloads on parameter search
2025-05-30 07:21:36,647 - INFO - Testing GET parameter: query
2025-05-30 07:21:36,648 - INFO - Testing php_injection payloads on parameter query
2025-05-30 07:21:38,739 - INFO - Testing xss payloads on parameter query
2025-05-30 07:21:40,841 - INFO - Testing sql_injection payloads on parameter query
2025-05-30 07:21:42,925 - INFO - Testing command_injection payloads on parameter query
2025-05-30 07:21:46,230 - INFO - Testing path_traversal payloads on parameter query
2025-05-30 07:21:48,322 - INFO - Testing json_injection payloads on parameter query
2025-05-30 07:21:50,419 - INFO - Testing header_injection payloads on parameter query
2025-05-30 07:21:52,529 - INFO - Testing file_upload payloads on parameter query
2025-05-30 07:21:54,608 - INFO - Testing ldap_injection payloads on parameter query
2025-05-30 07:21:56,690 - INFO - Testing template_injection payloads on parameter query
2025-05-30 07:21:58,784 - INFO - Testing GET parameter: filter
2025-05-30 07:21:58,785 - INFO - Testing php_injection payloads on parameter filter
2025-05-30 07:22:00,894 - INFO - Testing xss payloads on parameter filter
2025-05-30 07:22:02,985 - INFO - Testing sql_injection payloads on parameter filter
2025-05-30 07:22:05,080 - INFO - Testing command_injection payloads on parameter filter
2025-05-30 07:22:07,170 - INFO - Testing path_traversal payloads on parameter filter
2025-05-30 07:22:09,252 - INFO - Testing json_injection payloads on parameter filter
2025-05-30 07:22:11,331 - INFO - Testing header_injection payloads on parameter filter
2025-05-30 07:22:13,436 - INFO - Testing file_upload payloads on parameter filter
2025-05-30 07:22:15,524 - INFO - Testing ldap_injection payloads on parameter filter
2025-05-30 07:22:17,622 - INFO - Testing template_injection payloads on parameter filter
2025-05-30 07:22:19,725 - INFO - Testing GET parameter: sort
2025-05-30 07:22:19,727 - INFO - Testing php_injection payloads on parameter sort
2025-05-30 07:22:21,819 - INFO - Testing xss payloads on parameter sort
2025-05-30 07:22:23,915 - INFO - Testing sql_injection payloads on parameter sort
