#!/usr/bin/env python3
"""
Quick Test Runner for tuber92sv Security Testing
===============================================

This script provides a simplified interface for running security tests
against your authorized website.

Usage: python run_test.py
"""

import os
import sys
import subprocess
from colorama import init, Fore, Style

# Initialize colorama
init(autoreset=True)

def print_banner():
    """Print the security testing banner."""
    print(f"{Fore.CYAN}{'='*70}")
    print(f"{Fore.CYAN}    AUTHORIZED SECURITY TESTING SUITE")
    print(f"{Fore.CYAN}    tuber92sv Website Security Assessment")
    print(f"{Fore.CYAN}{'='*70}")
    print(f"{Fore.YELLOW}⚠️  IMPORTANT: Only use this on websites you own!")
    print(f"{Fore.CYAN}{'='*70}{Style.RESET_ALL}\n")

def check_dependencies():
    """Check if required dependencies are installed."""
    print(f"{Fore.CYAN}[INFO]{Style.RESET_ALL} Checking dependencies...")
    
    try:
        import requests
        import beautifulsoup4
        import colorama
        print(f"{Fore.GREEN}[SUCCESS]{Style.RESET_ALL} All dependencies are installed.")
        return True
    except ImportError as e:
        print(f"{Fore.RED}[ERROR]{Style.RESET_ALL} Missing dependency: {e}")
        print(f"{Fore.YELLOW}[INFO]{Style.RESET_ALL} Installing dependencies...")
        
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
            print(f"{Fore.GREEN}[SUCCESS]{Style.RESET_ALL} Dependencies installed successfully.")
            return True
        except subprocess.CalledProcessError:
            print(f"{Fore.RED}[ERROR]{Style.RESET_ALL} Failed to install dependencies.")
            return False

def get_target_url():
    """Get the target URL from user input."""
    default_url = "https://bytesurfer.ghxst.me/projects.php"
    
    print(f"{Fore.CYAN}Target URL Configuration:")
    print(f"{Fore.CYAN}Default: {default_url}")
    
    user_input = input(f"{Fore.YELLOW}Enter target URL (or press Enter for default): {Style.RESET_ALL}")
    
    if user_input.strip():
        return user_input.strip()
    else:
        return default_url

def confirm_authorization(target_url):
    """Confirm user has authorization to test the target."""
    print(f"\n{Fore.YELLOW}{'='*50}")
    print(f"{Fore.YELLOW}AUTHORIZATION CONFIRMATION")
    print(f"{Fore.YELLOW}{'='*50}")
    print(f"{Fore.YELLOW}Target: {target_url}")
    print(f"{Fore.YELLOW}{'='*50}")
    print(f"{Fore.RED}⚠️  WARNING: This will perform security testing on the target website.")
    print(f"{Fore.RED}⚠️  Only proceed if you OWN this website or have EXPLICIT permission.")
    print(f"{Fore.RED}⚠️  Unauthorized testing may violate laws and regulations.")
    print(f"{Fore.YELLOW}{'='*50}")
    
    while True:
        confirmation = input(f"{Fore.YELLOW}Do you have authorization to test this target? (yes/no): {Style.RESET_ALL}")
        
        if confirmation.lower() in ['yes', 'y']:
            return True
        elif confirmation.lower() in ['no', 'n']:
            return False
        else:
            print(f"{Fore.RED}Please enter 'yes' or 'no'.{Style.RESET_ALL}")

def run_security_test(target_url):
    """Run the security test."""
    print(f"\n{Fore.CYAN}[INFO]{Style.RESET_ALL} Starting security test...")
    print(f"{Fore.CYAN}[INFO]{Style.RESET_ALL} Target: {target_url}")
    
    try:
        # Run the security tester
        result = subprocess.run([
            sys.executable, 
            "security_tester.py", 
            target_url
        ], capture_output=False, text=True)
        
        if result.returncode == 0:
            print(f"\n{Fore.GREEN}[SUCCESS]{Style.RESET_ALL} Security test completed successfully!")
        else:
            print(f"\n{Fore.RED}[ERROR]{Style.RESET_ALL} Security test failed with return code: {result.returncode}")
            
    except FileNotFoundError:
        print(f"{Fore.RED}[ERROR]{Style.RESET_ALL} security_tester.py not found in current directory.")
    except Exception as e:
        print(f"{Fore.RED}[ERROR]{Style.RESET_ALL} Failed to run security test: {e}")

def show_menu():
    """Show the main menu."""
    print(f"\n{Fore.CYAN}Security Testing Options:")
    print(f"{Fore.CYAN}1. Run comprehensive security test")
    print(f"{Fore.CYAN}2. View test payloads")
    print(f"{Fore.CYAN}3. Check dependencies")
    print(f"{Fore.CYAN}4. Exit")
    
    choice = input(f"{Fore.YELLOW}Select an option (1-4): {Style.RESET_ALL}")
    return choice

def view_payloads():
    """Display information about test payloads."""
    print(f"\n{Fore.CYAN}Test Payload Categories:")
    print(f"{Fore.CYAN}{'='*40}")
    
    try:
        import test_payloads
        
        for category, payloads in test_payloads.ALL_PAYLOADS.items():
            print(f"{Fore.GREEN}{category.replace('_', ' ').title()}: {len(payloads)} payloads")
        
        print(f"\n{Fore.YELLOW}Example payloads:")
        print(f"{Fore.YELLOW}PHP Injection: {test_payloads.PHP_INJECTION_PAYLOADS[0]}")
        print(f"{Fore.YELLOW}XSS: {test_payloads.XSS_PAYLOADS[0]}")
        print(f"{Fore.YELLOW}SQL Injection: {test_payloads.SQL_INJECTION_PAYLOADS[0]}")
        
    except ImportError:
        print(f"{Fore.RED}[ERROR]{Style.RESET_ALL} test_payloads.py not found.")

def main():
    """Main function."""
    print_banner()
    
    while True:
        choice = show_menu()
        
        if choice == '1':
            # Run comprehensive test
            if not check_dependencies():
                continue
                
            target_url = get_target_url()
            
            if confirm_authorization(target_url):
                run_security_test(target_url)
            else:
                print(f"{Fore.RED}[ABORTED]{Style.RESET_ALL} Testing cancelled - authorization not confirmed.")
        
        elif choice == '2':
            # View payloads
            view_payloads()
        
        elif choice == '3':
            # Check dependencies
            check_dependencies()
        
        elif choice == '4':
            # Exit
            print(f"{Fore.CYAN}[INFO]{Style.RESET_ALL} Goodbye!")
            break
        
        else:
            print(f"{Fore.RED}[ERROR]{Style.RESET_ALL} Invalid choice. Please select 1-4.")
        
        input(f"\n{Fore.CYAN}Press Enter to continue...{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
